import React, { useState } from 'react';
import ChatComponent from './ChatComponent';
import UploadComponent from './UploadComponent';
import Sidebar from './Sidebar';

function App() {
  const [activeSection, setActiveSection] = useState('chat');

  const renderContent = () => {
    switch (activeSection) {
      case 'chat':
      case 'database':
        return <ChatComponent />;
      case 'upload':
      case 'storage':
        return <UploadComponent />;
      case 'embed':
      case 'integrations':
        return (
          <div className="p-8">
            <h2 className="text-2xl font-bold text-white mb-4">Embed Chatbot</h2>
            <p className="text-gray-300">Funcionalidade de embed em desenvolvimento...</p>
          </div>
        );
      default:
        return (
          <div className="p-8">
            <h2 className="text-2xl font-bold text-white mb-4">
              {activeSection.charAt(0).toUpperCase() + activeSection.slice(1)}
            </h2>
            <p className="text-gray-300">Esta funcionalidade está em desenvolvimento...</p>
          </div>
        );
    }
  };

  return (
    <div className="h-screen w-screen bg-gray-900 overflow-hidden">
      <Sidebar
        activeSection={activeSection}
        onSectionChange={setActiveSection}
      />
      <main className="main-content bg-gray-900">
        {renderContent()}
      </main>
    </div>
  );
}

export default App;
