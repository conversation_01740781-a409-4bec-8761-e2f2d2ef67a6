import React, { useState } from 'react';
import ChatComponent from './ChatComponent';
import UploadComponent from './UploadComponent';
import EmbedComponent from './EmbedComponent';
import SimpleSidebar from './SimpleSidebar';

function App() {
  const [activeSection, setActiveSection] = useState('chat');

  const renderContent = () => {
    switch (activeSection) {
      case 'chat':
      case 'database':
        return <ChatComponent />;
      case 'upload':
      case 'storage':
        return <UploadComponent />;
      case 'embed':
      case 'integrations':
        return <EmbedComponent />;
      default:
        return (
          <div className="p-8">
            <h2 className="text-2xl font-bold text-white mb-4">
              {activeSection.charAt(0).toUpperCase() + activeSection.slice(1)}
            </h2>
            <p className="text-gray-300">Esta funcionalidade está em desenvolvimento...</p>
          </div>
        );
    }
  };

  return (
    <div style={{ display: 'flex', height: '100vh', backgroundColor: '#111827' }}>
      <SimpleSidebar
        activeSection={activeSection}
        onSectionChange={setActiveSection}
      />
      <main style={{ flex: 1, overflowY: 'auto', backgroundColor: '#111827' }}>
        {renderContent()}
      </main>
    </div>
  );
}

export default App;
