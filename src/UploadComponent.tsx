import React, { useState } from 'react';
import { FiUpload, FiFile, <PERSON><PERSON>heck, <PERSON>X, FiLoader } from 'react-icons/fi';

const UploadComponent: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [message, setMessage] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setSelectedFile(event.target.files[0]);
      setMessage('');
    } else {
      setSelectedFile(null);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      setMessage('Por favor, selecione um arquivo PDF para upload.');
      return;
    }

    if (selectedFile.type !== 'application/pdf') {
      setMessage('Apenas arquivos PDF são permitidos.');
      return;
    }

    setIsLoading(true);
    setMessage('Enviando e processando o arquivo...');

    const formData = new FormData();
    formData.append('file', selectedFile);

    try {
      const response = await fetch('http://127.0.0.1:8000/api/upload_pdf', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        setMessage(data.message || 'Arquivo enviado e processado com sucesso!');
        setSelectedFile(null);
      } else {
        const errorData = await response.json();
        setMessage(errorData.detail || 'Erro ao enviar o arquivo.');
      }
    } catch (error) {
      console.error('Erro ao fazer upload:', error);
      setMessage('Erro de conexão. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100%', backgroundColor: '#030712' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '20px 24px',
        borderBottom: '1px solid #1f2937',
        backgroundColor: '#111827'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <div style={{
            width: '32px',
            height: '32px',
            backgroundColor: '#10b981',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <FiUpload style={{ color: '#111827', fontSize: '16px' }} />
          </div>
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: '#ffffff', margin: 0 }}>Upload de Documentos</h2>
            <span style={{ fontSize: '14px', color: '#9ca3af' }}>Carrega os teus PDFs para análise</span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div style={{ flex: 1, padding: '32px', overflowY: 'auto' }}>
        <div style={{ maxWidth: '600px', margin: '0 auto' }}>
          {/* Upload Card */}
          <div style={{
            backgroundColor: '#111827',
            border: '1px solid #1f2937',
            borderRadius: '12px',
            padding: '32px',
            textAlign: 'center'
          }}>
            <div style={{
              width: '80px',
              height: '80px',
              backgroundColor: '#1f2937',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 24px'
            }}>
              <FiUpload style={{ color: '#10b981', fontSize: '32px' }} />
            </div>

            <h3 style={{ fontSize: '24px', fontWeight: 'bold', color: '#ffffff', marginBottom: '8px' }}>
              Carrega os teus documentos
            </h3>
            <p style={{ color: '#9ca3af', marginBottom: '32px', fontSize: '16px' }}>
              Seleciona ficheiros PDF para análise e chat inteligente
            </p>

            {/* File Input */}
            <div style={{ marginBottom: '24px' }}>
              <input
                type="file"
                accept=".pdf"
                onChange={handleFileChange}
                style={{ display: 'none' }}
                id="file-upload"
              />
              <label
                htmlFor="file-upload"
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '12px',
                  padding: '16px 24px',
                  backgroundColor: '#10b981',
                  color: '#111827',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontWeight: '600',
                  fontSize: '16px',
                  border: 'none',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#059669';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#10b981';
                }}
              >
                <FiFile style={{ fontSize: '20px' }} />
                Escolher ficheiro PDF
              </label>
            </div>

            {/* Selected File Display */}
            {selectedFile && (
              <div style={{
                backgroundColor: '#1f2937',
                border: '1px solid #374151',
                borderRadius: '8px',
                padding: '16px',
                marginBottom: '24px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <FiFile style={{ color: '#10b981', fontSize: '20px' }} />
                  <div>
                    <p style={{ color: '#ffffff', fontWeight: '500', margin: 0 }}>
                      {selectedFile.name}
                    </p>
                    <p style={{ color: '#9ca3af', fontSize: '14px', margin: 0 }}>
                      {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedFile(null)}
                  style={{
                    backgroundColor: 'transparent',
                    border: 'none',
                    color: '#ef4444',
                    cursor: 'pointer',
                    padding: '4px'
                  }}
                >
                  <FiX style={{ fontSize: '20px' }} />
                </button>
              </div>
            )}

            {/* Upload Button */}
            <button
              onClick={handleUpload}
              disabled={!selectedFile || isLoading}
              style={{
                width: '100%',
                padding: '16px',
                backgroundColor: selectedFile && !isLoading ? '#10b981' : '#374151',
                color: selectedFile && !isLoading ? '#111827' : '#9ca3af',
                border: 'none',
                borderRadius: '8px',
                fontSize: '16px',
                fontWeight: '600',
                cursor: selectedFile && !isLoading ? 'pointer' : 'not-allowed',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '12px',
                transition: 'all 0.2s'
              }}
            >
              {isLoading ? (
                <>
                  <FiLoader style={{ fontSize: '20px', animation: 'spin 1s linear infinite' }} />
                  A processar...
                </>
              ) : (
                <>
                  <FiUpload style={{ fontSize: '20px' }} />
                  Carregar e processar
                </>
              )}
            </button>
          </div>

          {/* Message Card */}
          {message && (
            <div style={{
              marginTop: '24px',
              padding: '16px',
              borderRadius: '12px',
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              backgroundColor: message.includes('sucesso')
                ? 'rgba(16, 185, 129, 0.2)'
                : message.includes('Erro')
                ? 'rgba(239, 68, 68, 0.2)'
                : 'rgba(59, 130, 246, 0.2)',
              border: `1px solid ${
                message.includes('sucesso')
                  ? '#10b981'
                  : message.includes('Erro')
                  ? '#ef4444'
                  : '#3b82f6'
              }`,
              color: message.includes('sucesso')
                ? '#10b981'
                : message.includes('Erro')
                ? '#ef4444'
                : '#3b82f6'
            }}>
              <div style={{ fontSize: '20px' }}>
                {message.includes('sucesso') ? (
                  <FiCheck />
                ) : message.includes('Erro') ? (
                  <FiX />
                ) : (
                  <FiLoader style={{ animation: 'spin 1s linear infinite' }} />
                )}
              </div>
              <span style={{ fontWeight: '500' }}>{message}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UploadComponent;
