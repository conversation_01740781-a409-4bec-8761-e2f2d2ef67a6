import React, { useState } from 'react';

const EmbedComponent: React.FC = () => {
  const [embedCode, setEmbedCode] = useState('');
  const [customization, setCustomization] = useState({
    theme: 'dark',
    primaryColor: '#10b981',
    width: '400px',
    height: '600px',
    position: 'bottom-right'
  });

  const generateEmbedCode = () => {
    const code = `<!-- RAGPinecone Chatbot Embed -->
<div id="ragpinecone-chatbot"></div>
<script>
  (function() {
    var script = document.createElement('script');
    script.src = 'https://your-domain.com/embed/chatbot.js';
    script.async = true;
    script.onload = function() {
      RAGPineconeChatbot.init({
        containerId: 'ragpinecone-chatbot',
        theme: '${customization.theme}',
        primaryColor: '${customization.primaryColor}',
        width: '${customization.width}',
        height: '${customization.height}',
        position: '${customization.position}',
        apiKey: 'YOUR_API_KEY_HERE'
      });
    };
    document.head.appendChild(script);
  })();
</script>`;
    setEmbedCode(code);
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(embedCode);
    alert('Código copiado para a área de transferência!');
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-4">Embed Chatbot</h1>
        <p className="text-gray-300 text-lg">
          Integre o chatbot RAGPinecone no seu website com apenas algumas linhas de código.
        </p>
      </div>

      {/* Customization Panel */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Personalização</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Tema
              </label>
              <select
                value={customization.theme}
                onChange={(e) => setCustomization({...customization, theme: e.target.value})}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                <option value="dark">Escuro</option>
                <option value="light">Claro</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Cor Principal
              </label>
              <input
                type="color"
                value={customization.primaryColor}
                onChange={(e) => setCustomization({...customization, primaryColor: e.target.value})}
                className="w-full h-10 bg-gray-700 border border-gray-600 rounded-md"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Largura
                </label>
                <input
                  type="text"
                  value={customization.width}
                  onChange={(e) => setCustomization({...customization, width: e.target.value})}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="400px"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Altura
                </label>
                <input
                  type="text"
                  value={customization.height}
                  onChange={(e) => setCustomization({...customization, height: e.target.value})}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="600px"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Posição
              </label>
              <select
                value={customization.position}
                onChange={(e) => setCustomization({...customization, position: e.target.value})}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                <option value="bottom-right">Inferior Direita</option>
                <option value="bottom-left">Inferior Esquerda</option>
                <option value="top-right">Superior Direita</option>
                <option value="top-left">Superior Esquerda</option>
                <option value="center">Centro</option>
              </select>
            </div>

            <button
              onClick={generateEmbedCode}
              className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
            >
              Gerar Código de Embed
            </button>
          </div>
        </div>

        {/* Preview */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Pré-visualização</h2>
          <div className="bg-gray-700 rounded-lg p-4 h-64 flex items-center justify-center">
            <div 
              className="bg-gray-600 rounded-lg p-4 shadow-lg"
              style={{
                backgroundColor: customization.theme === 'dark' ? '#374151' : '#f3f4f6',
                borderColor: customization.primaryColor,
                borderWidth: '2px',
                borderStyle: 'solid'
              }}
            >
              <div className="flex items-center mb-2">
                <div 
                  className="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: customization.primaryColor }}
                ></div>
                <span className={customization.theme === 'dark' ? 'text-white' : 'text-gray-800'}>
                  RAGPinecone Chat
                </span>
              </div>
              <div className="text-xs text-gray-500">
                {customization.width} × {customization.height}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Generated Code */}
      {embedCode && (
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white">Código de Embed</h2>
            <button
              onClick={copyToClipboard}
              className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
            >
              Copiar Código
            </button>
          </div>
          <pre className="bg-gray-900 rounded-md p-4 overflow-x-auto">
            <code className="text-green-400 text-sm">{embedCode}</code>
          </pre>
        </div>
      )}

      {/* Instructions */}
      <div className="bg-gray-800 rounded-lg p-6 mt-8">
        <h2 className="text-xl font-semibold text-white mb-4">Instruções de Instalação</h2>
        <div className="space-y-4 text-gray-300">
          <div>
            <h3 className="font-medium text-white mb-2">1. Copie o código</h3>
            <p>Use o botão "Gerar Código de Embed" e depois "Copiar Código".</p>
          </div>
          <div>
            <h3 className="font-medium text-white mb-2">2. Cole no seu website</h3>
            <p>Adicione o código HTML gerado na página onde pretende que o chatbot apareça.</p>
          </div>
          <div>
            <h3 className="font-medium text-white mb-2">3. Configure a API Key</h3>
            <p>Substitua "YOUR_API_KEY_HERE" pela sua chave de API do RAGPinecone.</p>
          </div>
          <div>
            <h3 className="font-medium text-white mb-2">4. Teste</h3>
            <p>Recarregue a página e o chatbot deve aparecer na posição configurada.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmbedComponent;
