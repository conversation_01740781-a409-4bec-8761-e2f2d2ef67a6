import React, { useState } from 'react';
import {
  HiHome,
  HiChatBubbleLeftRight,
  HiDocumentArrowUp,
  <PERSON><PERSON>og<PERSON><PERSON>ooth,
  HiCircleStack,
  HiShieldCheck,
  HiCloudArrowUp,
  HiCpuChip,
  HiBolt,
  HiExclamationTriangle,
  HiChartBar,
  HiListBullet,
  HiDocumentText,
  HiSquares2X2,
  HiTableCells,
  HiCommandLine,
  HiLightBulb,
  HiDocumentDuplicate,
  HiPuzzlePiece,
  HiBars3,
  HiXMark
} from 'react-icons/hi2';

interface SidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

interface MenuItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  category?: string;
}

const menuItems: MenuItem[] = [
  { id: 'overview', label: 'Project overview', icon: HiHome },
  { id: 'table-editor', label: 'Table Editor', icon: HiTableCells },
  { id: 'sql-editor', label: 'SQL Editor', icon: HiCommandLine },

  { id: 'database', label: 'Chat com Documentos', icon: HiChatBubbleLeftRight, category: 'main' },
  { id: 'authentication', label: 'Authentication', icon: HiShieldCheck },
  { id: 'storage', label: 'Upload de Ficheiros', icon: HiDocumentArrowUp },
  { id: 'edge-functions', label: 'Edge Functions', icon: HiCpuChip },
  { id: 'realtime', label: 'Realtime', icon: HiBolt },

  { id: 'advisors', label: 'Advisors', icon: HiLightBulb },
  { id: 'reports', label: 'Reports', icon: HiChartBar },
  { id: 'logs', label: 'Logs', icon: HiListBullet },
  { id: 'api-docs', label: 'API Docs', icon: HiDocumentText },
  { id: 'integrations', label: 'Embed Chatbot', icon: HiPuzzlePiece },

  { id: 'settings', label: 'Project Settings', icon: HiCog6Tooth },
];

// Mapear as seções existentes para os novos IDs
const sectionMapping: { [key: string]: string } = {
  'chat': 'database',
  'upload': 'storage',
  'embed': 'integrations'
};

const Sidebar: React.FC<SidebarProps> = ({ activeSection, onSectionChange }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleItemClick = (itemId: string) => {
    // Mapear de volta para as seções originais se necessário
    const originalSection = Object.keys(sectionMapping).find(
      key => sectionMapping[key] === itemId
    ) || itemId;

    onSectionChange(originalSection);
  };

  const getCurrentActiveId = () => {
    return sectionMapping[activeSection] || activeSection;
  };

  const renderSeparator = () => (
    <div className="my-2">
      <div className="h-px bg-gray-700 mx-3"></div>
    </div>
  );

  return (
    <div
      className={`fixed top-0 left-0 h-screen bg-gray-900 border-r border-gray-700 transition-all duration-300 ease-in-out z-50 ${
        isExpanded ? 'w-64' : 'w-16'
      }`}
    >
      <aside className="flex flex-col h-full"
        onMouseEnter={() => setIsExpanded(true)}
        onMouseLeave={() => setIsExpanded(false)}
      >
        {/* Header */}
        <div className="p-4 border-b border-gray-700 flex-shrink-0">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center flex-shrink-0">
              <span className="text-white font-bold text-sm">R</span>
            </div>
            <div className={`ml-3 overflow-hidden transition-all duration-300 ${
              isExpanded ? 'opacity-100 w-auto' : 'opacity-0 w-0'
            }`}>
              <h1 className="text-white font-semibold text-lg whitespace-nowrap">
                RAGPinecone
              </h1>
              <div className="flex items-center mt-1">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-gray-400 text-sm ml-2">Online</span>
              </div>
            </div>
          </div>
        </div>

        {/* Menu Items */}
        <div className="flex-1 py-4 overflow-y-auto">
          <nav className="px-2 space-y-1">
            {menuItems.map((item, index) => {
              const isActive = getCurrentActiveId() === item.id;
              const showSeparatorBefore =
                (index === 3) || // Before Database
                (index === 8) || // Before Advisors
                (index === 13);  // Before Settings

              return (
                <React.Fragment key={item.id}>
                  {showSeparatorBefore && renderSeparator()}

                  <button
                    onClick={() => handleItemClick(item.id)}
                    className={`w-full flex items-center px-3 py-2.5 text-left transition-all duration-200 group relative rounded-lg ${
                      isActive
                        ? 'bg-gray-800 text-white border-r-2 border-green-500'
                        : 'text-gray-400 hover:text-white hover:bg-gray-800'
                    }`}
                  >
                    <div className="flex items-center min-w-0 flex-1">
                      <item.icon
                        className={`w-5 h-5 flex-shrink-0 ${
                          isActive ? 'text-green-500' : 'text-gray-400 group-hover:text-white'
                        }`}
                      />
                      <span className={`ml-3 text-sm font-medium whitespace-nowrap transition-all duration-300 ${
                        isExpanded ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-2'
                      }`}>
                        {item.label}
                      </span>
                    </div>

                    {/* Tooltip para quando não está expandido */}
                    {!isExpanded && (
                      <div className="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50 border border-gray-700">
                        {item.label}
                      </div>
                    )}
                  </button>
                </React.Fragment>
              );
            })}
          </nav>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-700 p-4 flex-shrink-0">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-gray-300 text-xs">U</span>
            </div>
            <div className={`ml-3 overflow-hidden transition-all duration-300 ${
              isExpanded ? 'opacity-100 w-auto' : 'opacity-0 w-0'
            }`}>
              <p className="text-white text-sm font-medium whitespace-nowrap">Utilizador</p>
              <p className="text-gray-400 text-xs whitespace-nowrap">Admin</p>
            </div>
          </div>
        </div>
      </aside>
    </div>
  );
};

export default Sidebar;
