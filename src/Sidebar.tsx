import React from 'react';
import {
  HiHome,
  HiTableCells,
  HiCommandLine,
  HiCircleStack,
  HiShieldCheck,
  HiCloudArrowUp,
  HiCpuChip,
  HiBolt,
  HiLightBulb,
  HiChartBar,
  HiListBullet,
  HiDocumentText,
  HiPuzzlePiece,
  HiCog6Tooth
} from 'react-icons/hi2';

interface SidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

interface MenuItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
}

const menuItems: MenuItem[] = [
  // Primeira secção
  { id: 'overview', label: 'Project overview', icon: HiHome },
  { id: 'table-editor', label: 'Table Editor', icon: HiTableCells },
  { id: 'sql-editor', label: 'SQL Editor', icon: HiCommandLine },
  
  // Segunda secção - Principal
  { id: 'database', label: 'Database', icon: HiCircleStack },
  { id: 'authentication', label: 'Authentication', icon: HiShieldCheck },
  { id: 'storage', label: 'Storage', icon: HiCloudArrowUp },
  { id: 'edge-functions', label: 'Edge Functions', icon: HiCpuChip },
  { id: 'realtime', label: 'Realtime', icon: HiBolt },
  
  // Terceira secção - Ferramentas
  { id: 'advisors', label: 'Advisors', icon: HiLightBulb },
  { id: 'reports', label: 'Reports', icon: HiChartBar },
  { id: 'logs', label: 'Logs', icon: HiListBullet },
  { id: 'api-docs', label: 'API Docs', icon: HiDocumentText },
  { id: 'integrations', label: 'Integrations', icon: HiPuzzlePiece },
  
  // Quarta secção - Configurações
  { id: 'settings', label: 'Project Settings', icon: HiCog6Tooth },
];

// Mapear as seções existentes para os novos IDs
const sectionMapping: { [key: string]: string } = {
  'chat': 'database',
  'upload': 'storage',
  'embed': 'integrations'
};

const Sidebar: React.FC<SidebarProps> = ({ activeSection, onSectionChange }) => {
  const handleItemClick = (itemId: string) => {
    // Mapear de volta para as seções originais se necessário
    const originalSection = Object.keys(sectionMapping).find(
      key => sectionMapping[key] === itemId
    ) || itemId;
    
    onSectionChange(originalSection);
  };

  const getCurrentActiveId = () => {
    return sectionMapping[activeSection] || activeSection;
  };

  const renderSeparator = () => (
    <div className="my-3">
      <div className="h-px bg-gray-700 mx-4"></div>
    </div>
  );

  return (
    <div className="fixed left-0 top-0 h-screen w-64 bg-gray-900 border-r border-gray-700 flex flex-col z-50">
      {/* Header */}
      <div className="p-6 border-b border-gray-700">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">R</span>
          </div>
          <div className="ml-3">
            <h1 className="text-white font-semibold text-lg">RAGPinecone</h1>
            <div className="flex items-center mt-1">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-gray-400 text-sm ml-2">Online</span>
            </div>
          </div>
        </div>
      </div>

      {/* Menu Items */}
      <div className="flex-1 py-4 overflow-y-auto">
        <nav className="px-3">
          {menuItems.map((item, index) => {
            const isActive = getCurrentActiveId() === item.id;
            const showSeparatorBefore = 
              (index === 3) || // Before Database
              (index === 8) || // Before Advisors
              (index === 13);  // Before Settings

            return (
              <React.Fragment key={item.id}>
                {showSeparatorBefore && renderSeparator()}
                
                <button
                  onClick={() => handleItemClick(item.id)}
                  className={`w-full flex items-center px-3 py-2.5 text-left transition-all duration-200 rounded-lg mb-1 ${
                    isActive
                      ? 'bg-gray-800 text-white'
                      : 'text-gray-400 hover:text-white hover:bg-gray-800'
                    }`}
                >
                  <item.icon
                    className={`w-5 h-5 mr-3 ${
                      isActive ? 'text-green-500' : 'text-gray-400'
                    }`}
                  />
                  <span className="text-sm font-medium">
                    {item.label}
                  </span>
                </button>
              </React.Fragment>
            );
          })}
        </nav>
      </div>

      {/* Footer */}
      <div className="border-t border-gray-700 p-4">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center">
            <span className="text-gray-300 text-xs">U</span>
          </div>
          <div className="ml-3">
            <p className="text-white text-sm font-medium">Utilizador</p>
            <p className="text-gray-400 text-xs">Admin</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
