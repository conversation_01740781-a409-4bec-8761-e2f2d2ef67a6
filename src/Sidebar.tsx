import React from 'react';
import {
  HiHome,
  HiTableCells,
  HiCommandLine,
  HiCircleStack,
  HiShieldCheck,
  HiCloudArrowUp,
  HiCpuChip,
  HiBolt,
  HiLightBulb,
  HiChartBar,
  HiListBullet,
  HiDocumentText,
  HiPuzzlePiece,
  HiCog6Tooth
} from 'react-icons/hi2';

interface SidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

interface MenuItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
}

const menuItems: MenuItem[] = [
  // Primeira secção
  { id: 'overview', label: 'Project overview', icon: () => <span>🏠</span> },
  { id: 'table-editor', label: 'Table Editor', icon: () => <span>📊</span> },
  { id: 'sql-editor', label: 'SQL Editor', icon: () => <span>💻</span> },

  // Segunda secção - Principal
  { id: 'database', label: 'Database', icon: () => <span>🗄️</span> },
  { id: 'authentication', label: 'Authentication', icon: () => <span>🔐</span> },
  { id: 'storage', label: 'Storage', icon: () => <span>☁️</span> },
  { id: 'edge-functions', label: 'Edge Functions', icon: () => <span>⚡</span> },
  { id: 'realtime', label: 'Realtime', icon: () => <span>🔄</span> },

  // Terceira secção - Ferramentas
  { id: 'advisors', label: 'Advisors', icon: () => <span>💡</span> },
  { id: 'reports', label: 'Reports', icon: () => <span>📈</span> },
  { id: 'logs', label: 'Logs', icon: () => <span>📋</span> },
  { id: 'api-docs', label: 'API Docs', icon: () => <span>📚</span> },
  { id: 'integrations', label: 'Integrations', icon: () => <span>🧩</span> },

  // Quarta secção - Configurações
  { id: 'settings', label: 'Project Settings', icon: () => <span>⚙️</span> },
];

// Mapear as seções existentes para os novos IDs
const sectionMapping: { [key: string]: string } = {
  'chat': 'database',
  'upload': 'storage',
  'embed': 'integrations'
};

const Sidebar: React.FC<SidebarProps> = ({ activeSection, onSectionChange }) => {
  const handleItemClick = (itemId: string) => {
    // Mapear de volta para as seções originais se necessário
    const originalSection = Object.keys(sectionMapping).find(
      key => sectionMapping[key] === itemId
    ) || itemId;
    
    onSectionChange(originalSection);
  };

  const getCurrentActiveId = () => {
    return sectionMapping[activeSection] || activeSection;
  };

  const renderSeparator = () => (
    <div className="my-3">
      <div className="h-px bg-gray-700 mx-4"></div>
    </div>
  );

  return (
    <div
      className="sidebar-debug w-64 h-screen bg-gray-900 border-r border-gray-700 flex flex-col flex-shrink-0"
      style={{
        width: '256px',
        height: '100vh',
        backgroundColor: '#111827',
        borderRight: '2px solid #10b981',
        display: 'flex',
        flexDirection: 'column',
        flexShrink: 0,
        minWidth: '256px'
      }}
    >
      {/* Header */}
      <div className="p-6 border-b border-gray-700" style={{ padding: '24px', borderBottom: '1px solid #374151' }}>
        <div className="flex items-center" style={{ display: 'flex', alignItems: 'center' }}>
          <div
            className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center"
            style={{
              width: '32px',
              height: '32px',
              backgroundColor: '#10b981',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <span className="text-white font-bold text-sm" style={{ color: 'white', fontWeight: 'bold', fontSize: '14px' }}>R</span>
          </div>
          <div className="ml-3" style={{ marginLeft: '12px' }}>
            <h1 className="text-white font-semibold text-lg" style={{ color: 'white', fontWeight: '600', fontSize: '18px' }}>RAGPinecone</h1>
            <div className="flex items-center mt-1" style={{ display: 'flex', alignItems: 'center', marginTop: '4px' }}>
              <div
                className="w-2 h-2 bg-green-400 rounded-full"
                style={{ width: '8px', height: '8px', backgroundColor: '#4ade80', borderRadius: '50%' }}
              ></div>
              <span className="text-gray-400 text-sm ml-2" style={{ color: '#9ca3af', fontSize: '14px', marginLeft: '8px' }}>Online</span>
            </div>
          </div>
        </div>
      </div>

      {/* Menu Items */}
      <div
        className="flex-1 py-4 overflow-y-auto"
        style={{ flex: 1, padding: '16px 0', overflowY: 'auto' }}
      >
        <nav className="px-3" style={{ padding: '0 12px' }}>
          {menuItems.map((item, index) => {
            const isActive = getCurrentActiveId() === item.id;
            const showSeparatorBefore =
              (index === 3) || // Before Database
              (index === 8) || // Before Advisors
              (index === 13);  // Before Settings

            return (
              <React.Fragment key={item.id}>
                {showSeparatorBefore && renderSeparator()}

                <button
                  onClick={() => handleItemClick(item.id)}
                  className={`w-full flex items-center px-3 py-2.5 text-left transition-all duration-200 rounded-lg mb-1 ${
                    isActive
                      ? 'bg-gray-800 text-white'
                      : 'text-gray-400 hover:text-white hover:bg-gray-800'
                    }`}
                  style={{
                    width: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    padding: '10px 12px',
                    textAlign: 'left',
                    backgroundColor: isActive ? '#1f2937' : 'transparent',
                    color: isActive ? 'white' : '#9ca3af',
                    borderRadius: '8px',
                    marginBottom: '4px',
                    border: 'none',
                    cursor: 'pointer'
                  }}
                >
                  <div style={{ marginRight: '12px', fontSize: '16px' }}>
                    <item.icon />
                  </div>
                  <span
                    className="text-sm font-medium"
                    style={{ fontSize: '14px', fontWeight: '500' }}
                  >
                    {item.label}
                  </span>
                </button>
              </React.Fragment>
            );
          })}
        </nav>
      </div>

      {/* Footer */}
      <div
        className="border-t border-gray-700 p-4"
        style={{ borderTop: '1px solid #374151', padding: '16px' }}
      >
        <div className="flex items-center" style={{ display: 'flex', alignItems: 'center' }}>
          <div
            className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center"
            style={{
              width: '32px',
              height: '32px',
              backgroundColor: '#374151',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <span className="text-gray-300 text-xs" style={{ color: '#d1d5db', fontSize: '12px' }}>U</span>
          </div>
          <div className="ml-3" style={{ marginLeft: '12px' }}>
            <p className="text-white text-sm font-medium" style={{ color: 'white', fontSize: '14px', fontWeight: '500' }}>Utilizador</p>
            <p className="text-gray-400 text-xs" style={{ color: '#9ca3af', fontSize: '12px' }}>Admin</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
