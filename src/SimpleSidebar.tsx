import React from 'react';

interface SimpleSidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

const SimpleSidebar: React.FC<SimpleSidebarProps> = ({ activeSection, onSectionChange }) => {
  const menuItems = [
    { id: 'chat', label: 'Chat com Documentos' },
    { id: 'upload', label: 'Upload de Ficheiros' },
    { id: 'embed', label: 'Embed Chatbot' },
    { id: 'settings', label: 'Configurações' },
  ];

  return (
    <div 
      style={{
        width: '256px',
        height: '100vh',
        backgroundColor: '#111827',
        borderRight: '2px solid #10b981',
        display: 'flex',
        flexDirection: 'column',
        flexShrink: 0,
        position: 'relative',
        zIndex: 1000
      }}
    >
      {/* Header */}
      <div style={{ padding: '24px', borderBottom: '1px solid #374151' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div 
            style={{ 
              width: '32px', 
              height: '32px', 
              backgroundColor: '#10b981', 
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <span style={{ color: 'white', fontWeight: 'bold', fontSize: '14px' }}>R</span>
          </div>
          <div style={{ marginLeft: '12px' }}>
            <h1 style={{ color: 'white', fontWeight: '600', fontSize: '18px', margin: 0 }}>RAGPinecone</h1>
            <div style={{ display: 'flex', alignItems: 'center', marginTop: '4px' }}>
              <div 
                style={{ width: '8px', height: '8px', backgroundColor: '#4ade80', borderRadius: '50%' }}
              ></div>
              <span style={{ color: '#9ca3af', fontSize: '14px', marginLeft: '8px' }}>Online</span>
            </div>
          </div>
        </div>
      </div>

      {/* Menu Items */}
      <div style={{ flex: 1, padding: '16px 0', overflowY: 'auto' }}>
        <nav style={{ padding: '0 12px' }}>
          {menuItems.map((item) => {
            const isActive = activeSection === item.id;
            
            return (
              <button
                key={item.id}
                onClick={() => onSectionChange(item.id)}
                style={{
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  padding: '12px',
                  textAlign: 'left',
                  backgroundColor: isActive ? '#1f2937' : 'transparent',
                  color: isActive ? 'white' : '#9ca3af',
                  borderRadius: '8px',
                  marginBottom: '4px',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
                onMouseEnter={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.backgroundColor = '#1f2937';
                    e.currentTarget.style.color = 'white';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = '#9ca3af';
                  }
                }}
              >
                <span style={{ marginRight: '12px', fontSize: '16px' }}>
                  {item.id === 'chat' && '💬'}
                  {item.id === 'upload' && '📁'}
                  {item.id === 'embed' && '🔗'}
                  {item.id === 'settings' && '⚙️'}
                </span>
                {item.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Footer */}
      <div style={{ borderTop: '1px solid #374151', padding: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div 
            style={{
              width: '32px',
              height: '32px',
              backgroundColor: '#374151',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <span style={{ color: '#d1d5db', fontSize: '12px' }}>U</span>
          </div>
          <div style={{ marginLeft: '12px' }}>
            <p style={{ color: 'white', fontSize: '14px', fontWeight: '500', margin: 0 }}>Utilizador</p>
            <p style={{ color: '#9ca3af', fontSize: '12px', margin: 0 }}>Admin</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleSidebar;
