import React, { useState, useRef, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { FaDownload, FaTrash, FaPaperPlane, FaComments } from 'react-icons/fa';

interface Message {
  text: string;
  sender: 'user' | 'bot';
}

const ChatComponent: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isTyping, setIsTyping] = useState<boolean>(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(scrollToBottom, [messages]);

  const clearHistory = () => {
    setMessages([]);
  };

  const exportConversation = () => {
    const conversationText = messages
      .map((msg) => `${msg.sender === 'user' ? 'Você' : 'Bot'}: ${msg.text}`)
      .join('\n');
    const blob = new Blob([conversationText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'conversacao_chat.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const sendMessage = async () => {
    if (input.trim() === '') return;

    const currentInput = input;
    const userMessage: Message = { text: currentInput, sender: 'user' };
    setMessages((prevMessages) => [...prevMessages, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      // Preparar histórico de conversa no formato esperado pelo backend
      const chatHistory = messages.map(msg => ({
        user: msg.sender === 'user' ? msg.text : '',
        assistant: msg.sender === 'bot' ? msg.text : ''
      })).filter(msg => msg.user || msg.assistant);

      const response = await fetch('http://127.0.0.1:8000/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: currentInput,
          chat_history: chatHistory
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data && data.resposta_formatada) {
        const botMessage: Message = { text: data.resposta_formatada, sender: 'bot' };
        setMessages((prevMessages) => [...prevMessages, botMessage]);
      } else {
        throw new Error('Invalid response format: resposta_formatada is missing or null.');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages((prevMessages) => [
        ...prevMessages,
        { text: 'Hmm, parece que estou com problemas de conexão. Podes tentar novamente? 🔌', sender: 'bot' },
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInput(value);

    // Mostrar indicador de typing
    if (value.length > 0 && !isTyping) {
      setIsTyping(true);
    }

    // Limpar timeout anterior
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Esconder indicador após 1 segundo de inatividade
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
    }, 1000);

    // Se o input estiver vazio, esconder imediatamente
    if (value.length === 0) {
      setIsTyping(false);
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      sendMessage();
      setIsTyping(false);
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100%', backgroundColor: '#030712' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '20px 24px',
        borderBottom: '1px solid #1f2937',
        backgroundColor: '#111827'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <div style={{
            width: '32px',
            height: '32px',
            backgroundColor: '#10b981',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <span style={{ color: '#111827', fontSize: '14px' }}>💬</span>
          </div>
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: '#ffffff', margin: 0 }}>Chat com Documentos</h2>
            {isTyping && (
              <span style={{ fontSize: '12px', color: '#10b981', marginTop: '2px' }}>
                A escrever... ✍️
              </span>
            )}
          </div>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <button
            onClick={exportConversation}
            title="Exportar Conversa"
            style={{
              padding: '8px',
              color: '#9ca3af',
              backgroundColor: 'transparent',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.color = '#ffffff';
              e.currentTarget.style.backgroundColor = '#1f2937';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.color = '#9ca3af';
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <span style={{ fontSize: '14px' }}>💾</span>
          </button>
          <button
            onClick={clearHistory}
            title="Limpar Histórico"
            style={{
              padding: '8px',
              color: '#9ca3af',
              backgroundColor: 'transparent',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.color = '#ffffff';
              e.currentTarget.style.backgroundColor = '#1f2937';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.color = '#9ca3af';
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <span style={{ fontSize: '14px' }}>🗑️</span>
          </button>
        </div>
      </div>
      {/* Messages Area */}
      <div style={{ flex: 1, overflowY: 'auto', padding: '24px' }}>
        {messages.length === 0 ? (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
            <div style={{ textAlign: 'center' }}>
              {/* Welcome Card */}
              <div style={{
                backgroundColor: '#111827',
                borderRadius: '16px',
                border: '1px solid #1f2937',
                padding: '32px',
                maxWidth: '400px'
              }}>
                <div style={{
                  width: '64px',
                  height: '64px',
                  backgroundColor: '#1f2937',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto 16px'
                }}>
                  <span style={{ color: '#10b981', fontSize: '24px' }}>💬</span>
                </div>
                <h3 style={{ fontSize: '18px', fontWeight: '500', color: '#d1d5db', marginBottom: '8px' }}>Olá! 👋 Sou o teu assistente de documentos</h3>
                <p style={{ color: '#6b7280', marginBottom: '16px' }}>Podes fazer-me qualquer pergunta sobre os documentos que carregaste, ou simplesmente conversar comigo! 😊</p>

                {/* Exemplos de perguntas */}
                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginTop: '16px' }}>
                  <p style={{ fontSize: '14px', color: '#9ca3af', marginBottom: '8px' }}>Exemplos do que podes perguntar:</p>
                  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                    {[
                      "Olá! Como estás?",
                      "Que documentos tens?",
                      "Resume o documento X",
                      "Explica-me sobre..."
                    ].map((example, index) => (
                      <button
                        key={index}
                        onClick={() => setInput(example)}
                        style={{
                          padding: '6px 12px',
                          backgroundColor: '#1f2937',
                          border: '1px solid #374151',
                          borderRadius: '16px',
                          color: '#d1d5db',
                          fontSize: '12px',
                          cursor: 'pointer',
                          transition: 'all 0.2s'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = '#374151';
                          e.currentTarget.style.borderColor = '#10b981';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = '#1f2937';
                          e.currentTarget.style.borderColor = '#374151';
                        }}
                      >
                        {example}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', maxWidth: '800px', margin: '0 auto' }}>
            {messages.map((message, index) => (
              <div key={index} style={{
                display: 'flex',
                justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start'
              }}>
                <div style={{
                  maxWidth: message.sender === 'user' ? '70%' : '85%',
                  padding: message.sender === 'user' ? '12px 16px' : '20px 24px',
                  borderRadius: '12px',
                  backgroundColor: message.sender === 'user' ? '#10b981' : '#111827',
                  color: message.sender === 'user' ? '#111827' : '#f9fafb',
                  border: message.sender === 'user' ? 'none' : '1px solid #1f2937',
                  boxShadow: message.sender === 'user' ? 'none' : '0 2px 8px rgba(0, 0, 0, 0.1)'
                }}>
                  <div className="chat-message">
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>
                      {message.text}
                    </ReactMarkdown>
                  </div>
                </div>
              </div>
            ))}
            {isLoading && (
              <div style={{ display: 'flex', justifyContent: 'flex-start' }}>
                <div style={{
                  backgroundColor: '#111827',
                  color: '#f9fafb',
                  border: '1px solid #1f2937',
                  padding: '12px 16px',
                  borderRadius: '12px'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <div style={{ display: 'flex', gap: '4px' }}>
                      <div style={{
                        width: '8px',
                        height: '8px',
                        backgroundColor: '#10b981',
                        borderRadius: '50%',
                        animation: 'bounce 1.4s infinite ease-in-out'
                      }}></div>
                      <div style={{
                        width: '8px',
                        height: '8px',
                        backgroundColor: '#10b981',
                        borderRadius: '50%',
                        animation: 'bounce 1.4s infinite ease-in-out 0.16s'
                      }}></div>
                      <div style={{
                        width: '8px',
                        height: '8px',
                        backgroundColor: '#10b981',
                        borderRadius: '50%',
                        animation: 'bounce 1.4s infinite ease-in-out 0.32s'
                      }}></div>
                    </div>
                    <span style={{ fontSize: '14px', color: '#9ca3af' }}>A pensar... 🤔</span>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
      {/* Input Area */}
      <div style={{
        padding: '24px',
        borderTop: '1px solid #1f2937',
        backgroundColor: '#111827'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '16px',
          maxWidth: '800px',
          margin: '0 auto'
        }}>
          <input
            type="text"
            value={input}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder="Escreve aqui a tua mensagem... 💬"
            disabled={isLoading}
            style={{
              flex: 1,
              padding: '12px 16px',
              backgroundColor: '#1f2937',
              border: '1px solid #374151',
              borderRadius: '8px',
              color: '#f9fafb',
              fontSize: '16px',
              outline: 'none',
              opacity: isLoading ? 0.5 : 1,
              cursor: isLoading ? 'not-allowed' : 'text'
            }}
            onFocus={(e) => e.currentTarget.style.borderColor = '#10b981'}
            onBlur={(e) => e.currentTarget.style.borderColor = '#374151'}
          />
          <button
            onClick={sendMessage}
            disabled={isLoading || input.trim() === ''}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '12px 24px',
              backgroundColor: (isLoading || input.trim() === '') ? '#374151' : '#10b981',
              color: (isLoading || input.trim() === '') ? '#6b7280' : '#111827',
              borderRadius: '8px',
              fontWeight: '500',
              border: 'none',
              cursor: (isLoading || input.trim() === '') ? 'not-allowed' : 'pointer',
              transition: 'background-color 0.2s'
            }}
            onMouseEnter={(e) => {
              if (!isLoading && input.trim() !== '') {
                e.currentTarget.style.backgroundColor = '#059669';
              }
            }}
            onMouseLeave={(e) => {
              if (!isLoading && input.trim() !== '') {
                e.currentTarget.style.backgroundColor = '#10b981';
              }
            }}
          >
            <span style={{ fontSize: '14px' }}>📤</span>
            <span>Enviar</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatComponent;