import os
import io
import base64
import fitz  # PyMuPDF
import cv2
import numpy as np
from PIL import Image, ImageEnhance
from dotenv import load_dotenv
import json
import time

load_dotenv()

import google.generativeai as genai

gemini_api_key = os.getenv("GEMINI_API_KEY")
if not gemini_api_key:
    raise ValueError("GEMINI_API_KEY não encontrada nas variáveis de ambiente")

genai.configure(api_key=gemini_api_key)

model = genai.GenerativeModel('gemini-1.5-flash')
print("✅ Configurando Gemini API")

try:
    # Testar a conexão com Gemini (apenas se não for quota exceeded)
    response = model.generate_content("Test connection")
    print("✅ Conexão com Gemini API estabelecida com sucesso")
except Exception as e:
    if "429" in str(e) and "quota" in str(e).lower():
        print(f"⚠️ Quota da API Gemini excedida: {e}")
        print("⚠️ O servidor irá iniciar, mas as funcionalidades de análise de imagem podem estar limitadas")
    else:
        print(f"⚠️ Erro ao conectar com Gemini API: {e}")
        raise

from pydantic import BaseModel, Field
from enum import Enum
from typing import List, Optional

class ContentType(str, Enum):
    TEXT = "text"
    LOGO = "logo"
    DIAGRAM = "diagram"
    TABLE = "table"
    ICON = "icon"
    IMAGE = "image"

class ImageAnalysis(BaseModel):
    detected_text: str = Field(description="Text content detected in the image")
    content_type: ContentType = Field(description="Type of visual content")
    description: str = Field(description="Detailed description of visual elements")
    confidence: float = Field(ge=0.0, le=1.0, description="Confidence score of the analysis")
    visual_elements: List[str] = Field(default_factory=list, description="List of identified visual elements")
    metadata: dict = Field(default_factory=dict, description="Additional metadata about the image")

class DocumentAnalysis(BaseModel):
    title: Optional[str] = Field(description="Document title if found")
    page_number: int = Field(description="Page number")
    content_type: str = Field(description="Type of content (text/image)")
    content: str = Field(description="The actual content")
    image_analysis: Optional[ImageAnalysis] = Field(description="Analysis of image content if present")

def encode_image_to_base64(image):
    """Converte uma imagem PIL para base64."""
    if isinstance(image, Image.Image):
        buffered = io.BytesIO()
        image.save(buffered, format="PNG")
        return base64.b64encode(buffered.getvalue()).decode()
    elif isinstance(image, np.ndarray):
        # Converter array numpy para PIL Image
        image_pil = Image.fromarray(image)
        buffered = io.BytesIO()
        image_pil.save(buffered, format="PNG")
        return base64.b64encode(buffered.getvalue()).decode()

def extract_text_from_image(image):
    """Extrai texto da imagem usando Gemini Vision."""
    retries = 0
    max_retries = 5
    delay = 20  # seconds
    while retries < max_retries:
        try:
            # Converter imagem para base64 não é necessário para Gemini, basta passar o objeto PIL
            response = model.generate_content(["Extraia e retorne apenas o texto encontrado nesta imagem, sem explicação adicional.", image])
            extracted_text = response.text
            if extracted_text:
                print(f"✅ Texto extraído com sucesso: {extracted_text[:100]}...")
            else:
                print("⚠️ Nenhum texto detectado na imagem")
            return extracted_text
        except Exception as e:
            if "429" in str(e):
                if "quota" in str(e).lower():
                    print(f"⚠️ Quota da API Gemini excedida na extração de texto: {e}")
                    return ""  # Retorna string vazia em vez de tentar novamente
                else:
                    print(f"⚠️ Rate limit atingido na extração de texto. Tentando novamente em {delay} segundos...")
                    time.sleep(delay)
                    retries += 1
            else:
                print(f"⚠️ Erro na extração de texto: {e}")
                return ""
    print("❌ Máximo de tentativas atingido na extração de texto.")
    return ""

def preprocess_image(image):
    """Aplica técnicas de pré-processamento para melhorar a qualidade da imagem."""
    try:
        # Converter PIL Image para array numpy/OpenCV
        img_array = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        
        # Redimensionar se a resolução for muito baixa (mínimo 300 DPI)
        min_dpi = 300
        if img_array.shape[0] < 1000 or img_array.shape[1] < 1000:
            scale = min_dpi / 72  # assumindo DPI padrão de 72
            img_array = cv2.resize(img_array, None, fx=scale, fy=scale, interpolation=cv2.INTER_CUBIC)
        
        # Converter para escala de cinza
        gray = cv2.cvtColor(img_array, cv2.COLOR_BGR2GRAY)
        
        # Remover ruído usando filtro bilateral
        denoised = cv2.bilateralFilter(gray, 9, 75, 75)
        
        # Melhorar contraste usando equalização adaptativa de histograma
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(denoised)
        
        # Binarização adaptativa
        binary = cv2.adaptiveThreshold(enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                     cv2.THRESH_BINARY, 11, 2)
        
        # Remover pequenos ruídos
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2,2))
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        # Converter de volta para PIL Image
        enhanced_image = Image.fromarray(cleaned)
        
        # Ajustar nitidez
        sharpener = ImageEnhance.Sharpness(enhanced_image)
        enhanced_image = sharpener.enhance(1.5)
        
        print("✅ Imagem pré-processada com sucesso")
        return enhanced_image
    except Exception as e:
        print(f"⚠️ Erro no pré-processamento da imagem: {e}")
        return image  # Retorna imagem original se houver erro

def extract_images_from_page(page):
    """Extract images from a PDF page using PyMuPDF."""
    images = []
    try:
        # Obter lista de imagens na página
        image_list = page.get_images()
        print(f"Found {len(image_list)} images in page")
        
        for img in image_list:
            try:
                # Extrair XREF da imagem
                xref = img[0]
                
                # Extrair dados da imagem
                base_image = page.parent.extract_image(xref)
                if base_image:
                    try:
                        # Converter bytes para PIL Image
                        image_bytes = base_image["image"]
                        img_buffer = io.BytesIO(image_bytes)
                        pil_image = Image.open(img_buffer)
                        
                        # Assegurar que a imagem está em modo RGB
                        if pil_image.mode in ('RGBA', 'LA', 'P'):
                            background = Image.new('RGB', pil_image.size, (255, 255, 255))
                            if pil_image.mode == 'P':
                                pil_image = pil_image.convert('RGBA')
                            background.paste(pil_image, mask=pil_image.split()[-1])
                            pil_image = background
                        elif pil_image.mode != 'RGB':
                            pil_image = pil_image.convert('RGB')
                        
                        # Pré-processar a imagem
                        enhanced_img = preprocess_image(pil_image)
                        images.append(enhanced_img)
                        
                        print(f"✅ Successfully processed image {xref}")
                        print(f"   Size: {enhanced_img.size}")
                        print(f"   Format: {base_image.get('ext', 'unknown')}")
                        
                    except Exception as e:
                        print(f"⚠️ Error processing image {xref}: {e}")
                        continue
                        
            except Exception as e:
                print(f"⚠️ Error extracting image {img[0]}: {e}")
                continue
                
    except Exception as e:
        print(f"⚠️ Error processing page images: {e}")
        
    return images

def analyze_image_with_gemini(image, ocr_text=""):
    """Analisa imagem usando Gemini Vision API com saída estruturada."""
    retries = 0
    max_retries = 5
    delay = 20  # seconds
    while retries < max_retries:
        try:
            extracted_text = extract_text_from_image(image)
            prompt = (
                "Analise esta imagem em detalhe e forneça informações em JSON com a seguinte estrutura:\n"
                "{\n"
                "  'content_type': 'text/logo/diagram/table/icon/image',\n"
                "  'visual_elements': ['lista de elementos presentes'],\n"
                "  'text_content': 'qualquer texto encontrado',\n"
                "  'description': 'descrição detalhada',\n"
                "  'confidence': 0.0 a 1.0\n"
                "}\n"
                f"\nTexto OCR detectado: {extracted_text if extracted_text else 'Nenhum texto detectado'}\n"
                "Retorne apenas o objeto JSON, nada mais."
            )
            response = model.generate_content([prompt, image])
            try:
                analysis_dict = json.loads(response.text)
            except json.JSONDecodeError:
                analysis_dict = {
                    "content_type": "image",
                    "visual_elements": [],
                    "text_content": extracted_text if extracted_text else "",
                    "description": response.text,
                    "confidence": 0.5
                }
            return analysis_dict
        except Exception as e:
            if "429" in str(e):
                if "quota" in str(e).lower():
                    print(f"⚠️ Quota da API Gemini excedida na análise da imagem: {e}")
                    # Retorna análise básica em vez de None
                    return {
                        "content_type": "image",
                        "visual_elements": [],
                        "text_content": "",
                        "description": "Análise não disponível devido a limitações de quota da API",
                        "confidence": 0.0
                    }
                else:
                    print(f"⚠️ Rate limit atingido na análise da imagem. Tentando novamente em {delay} segundos...")
                    time.sleep(delay)
                    retries += 1
            else:
                print(f"⚠️ Falha ao analisar imagem: {e}")
                return None
    print("❌ Máximo de tentativas atingido na análise da imagem.")
    return None


def process_pdf_with_images(pdf_path):
    """Process PDF using PyMuPDF, extracting text and images with analysis."""
    content_chunks = []
    print(f"\nProcessing PDF: {pdf_path} with PyMuPDF")
    doc = fitz.open(pdf_path)

    for page_num, page in enumerate(doc):
        # Extrair texto da página
        text = page.get_text("text")
        if text.strip():
            content_chunks.append({
                'type': 'text',
                'content': text,
                'page': page_num + 1,
            })
            print(f"  Extracted text chunk (Page: {page_num + 1})")

        # Extrair e analisar imagens da página
        extracted_images = extract_images_from_page(page)
        for img_idx, img in enumerate(extracted_images):
            description_analysis = analyze_image_with_gemini(img)
            if description_analysis:
                content_chunks.append({
                    'type': 'image_description',
                    'content': description_analysis['description'],
                    'page': page_num + 1,
                    'analysis': description_analysis,
                    'metadata': {
                        'source': os.path.basename(pdf_path),
                        'image_num': img_idx,
                        'element_type': 'Image_Fitz'
                    }
                })
                print(f"    ✅ Image extracted and analyzed via PyMuPDF (Page: {page_num + 1}, Image: {img_idx})")
            else:
                print(f"    ⚠️ Image extracted via PyMuPDF but analysis failed (Page: {page_num + 1}, Image: {img_idx})")

    doc.close()
    return content_chunks

def is_blank_image(pil_img, threshold=250):
    """Detecta se uma imagem PIL é praticamente em branco."""
    arr = np.array(pil_img.convert("L"))
    return np.mean(arr) > threshold

def extract_page_image_with_fitz(pdf_path, page_number):
    """Extrai a página como imagem usando PyMuPDF (fitz)."""
    doc = fitz.open(pdf_path)
    if 0 <= page_number < len(doc):
        page = doc[page_number]
        pix = page.get_pixmap(dpi=300)
        img = Image.open(io.BytesIO(pix.tobytes("png")))
        return img
    return None

if __name__ == "__main__":
    pdf_folder = os.getenv("PDF_FOLDER", "./pdfs")
    if not os.path.isdir(pdf_folder):
        print(f"⚠️ Pasta de PDFs não encontrada: {pdf_folder}")
    else:
        pdf_files = [f for f in os.listdir(pdf_folder) if f.lower().endswith('.pdf')]
        if not pdf_files:
            print(f"⚠️ Nenhum arquivo PDF encontrado em {pdf_folder}")
        else:
            print(f"🔎 Encontrados {len(pdf_files)} PDFs em {pdf_folder}")
            for pdf_file in pdf_files:
                pdf_path = os.path.join(pdf_folder, pdf_file)
                print(f"\n➡️ Processando: {pdf_path}")
                try:
                    result = process_pdf_with_images(pdf_path)
                    print(f"✅ Processamento concluído para {pdf_file}. Chunks extraídos: {len(result)}")
                except Exception as e:
                    print(f"❌ Erro ao processar {pdf_file}: {e}")
