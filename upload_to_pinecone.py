import os
import pdfplumber
import tiktoken
from pinecone import <PERSON>con<PERSON>
from unidecode import unidecode
import time
from dotenv.main import load_dotenv
import google.generativeai as genai
from pdf_image_processor import process_pdf_with_images

def chunk_text_with_tiktoken(text, chunk_size=150, overlap=75):  # Chunks menores com mais sobreposição
    """Split text into chunks using tiktoken for token counting."""
    enc = tiktoken.get_encoding("cl100k_base")
    tokens = enc.encode(text)
    chunks = []
    
    start = 0
    while start < len(tokens):
        end = start + chunk_size
        chunk_tokens = tokens[start:end]
        chunks.append(enc.decode(chunk_tokens))
        start = end - overlap
    
    return chunks

# ----------------------

# ----------------------
# Load Environment Variables
# ----------------------
load_dotenv()

# ----------------------
# Configuration Section
# ----------------------
gemini_api_key = os.getenv('GEMINI_API_KEY')
pinecone_api_key = os.getenv('PINECONE_API_KEY')
index_name = os.getenv('PINECONE_INDEX_NAME')
pdf_folder = os.getenv('PDF_FOLDER')

# Validação das variáveis de ambiente
if not all([gemini_api_key, pinecone_api_key, index_name, pdf_folder]):
    raise ValueError("""
    Por favor, configure todas as variáveis de ambiente necessárias no arquivo .env:
    - GEMINI_API_KEY
    - PINECONE_API_KEY
    - PINECONE_INDEX_NAME
    - PDF_FOLDER
    """)

# Verificar se o diretório de PDFs existe
if not os.path.isdir(pdf_folder):
    raise ValueError(f"O diretório de PDFs não existe: {pdf_folder}")

# ----------------------
# Resume Support - Set the Batch Number to Resume From
start_batch = 0  # Change this to resume from a later batch if interrupted
batch_size = 50  # Number of chunks to process per batch

# ----------------------
# Initialize Clients
pc = Pinecone(api_key=pinecone_api_key)
index = pc.Index(index_name)

print("✅ Configurando Gemini API")
try:
    genai.configure(api_key=gemini_api_key)

    generation_config = {
        "temperature": 0.7,
        "top_p": 0.9,
        "top_k": 40,
        "candidate_count": 1,
    }

    safety_settings = [
        {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
        {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
        {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
        {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
    ]

    # Tentar inicializar os modelos
    text_model = genai.GenerativeModel(
        model_name='gemini-1.5-flash',  # Usar modelo mais estável
        generation_config=generation_config,
        safety_settings=safety_settings
    )

    vision_model = genai.GenerativeModel(
        model_name='gemini-1.5-flash',  # Usar modelo mais estável
        generation_config=generation_config,
        safety_settings=safety_settings
    )

    # Testar se a API está funcional
    try:
        test_response = genai.embed_content(
            model="models/embedding-001",
            content="teste"
        )
        print("✅ API Gemini configurada com sucesso")
    except Exception as e:
        if "429" in str(e) or "quota" in str(e).lower():
            print(f"⚠️ Quota da API Gemini excedida: {e}")
            print("⚠️ O servidor irá iniciar, mas as funcionalidades de análise de imagem podem estar limitadas")
        else:
            print(f"⚠️ Erro ao testar API Gemini: {e}")

except Exception as e:
    print(f"❌ Erro ao configurar Gemini API: {e}")
    print("⚠️ O servidor irá iniciar sem funcionalidades de IA")
    text_model = None
    vision_model = None


# ----------------------
# Safe PDF Extraction Function
def extract_text(file_path):
    try:
        text = ""
        with pdfplumber.open(file_path) as pdf:
            for page in pdf.pages:
                # Extrair todo o texto da página
                words = page.extract_words(keep_blank_chars=True, x_tolerance=1, y_tolerance=1)
                
                # Ordenar palavras por posição (de cima para baixo, esquerda para direita)
                words.sort(key=lambda w: (-w['top'], w['x0']))
                
                # Reconstruir o texto mantendo a formatação aproximada
                current_line = []
                current_y = None
                
                for word in words:
                    if current_y is None:
                        current_y = word['top']
                    
                    # Se mudou de linha significativamente
                    if abs(word['top'] - current_y) > 5:
                        text += ' '.join(current_line) + '\n'
                        current_line = []
                        current_y = word['top']
                    
                    current_line.append(word['text'])
                
                if current_line:
                    text += ' '.join(current_line) + '\n'
                text += '\n'  # Separador entre páginas
        
        # Remover linhas em branco extras mas manter a estrutura
        text = '\n'.join(line for line in text.splitlines() if line.strip())
        return text
    except Exception as e:
        print(f"⚠️ Failed to process {file_path}: {e}")
        return None

# ----------------------
# Document Processing and Upload Function
def process_all_pdfs_and_upload_to_pinecone(genai, text_model, pdf_folder, specific_file_path=None):
    # Initialize Pinecone and get the index
    pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
    index_name = os.getenv('PINECONE_INDEX_NAME')

    try:
        indexes = pc.list_indexes().names()
        if index_name not in indexes:
            print(f"Creating new index '{index_name}'...")
            pc.create_index(
                name=index_name,
                dimension=768,  # Dimension of Gemini embeddings
                metric='cosine',
                spec={
                    "serverless": {
                        "cloud": "aws",
                        "region": "us-east-1"
                    }
                }
            )
            print(f"Index '{index_name}' created successfully.")
        else:
            print(f"Using existing index '{index_name}'...")

        index = pc.Index(index_name)
    except Exception as e:
        print(f"Error with Pinecone index: {e}")
        raise

    print("Starting document processing...")
    all_chunks = []

    files_to_process = []
    if specific_file_path:
        files_to_process.append(specific_file_path)
    else:
        for filename in os.listdir(pdf_folder):
            if filename.endswith(".pdf"):
                files_to_process.append(os.path.join(pdf_folder, filename))

    for file_path in files_to_process:
            print(f"Processing {os.path.basename(file_path)}...")

            # Process both text and images
            content_chunks = process_pdf_with_images(file_path)

            # Process each content chunk
            for i, chunk in enumerate(content_chunks):
                chunk_text = chunk['content']
                chunk_type = chunk['type']
                page_num = chunk['page']

                # Split text chunks if needed
                if chunk_type == 'text_chunk':
                    splits = chunk_text_with_tiktoken(chunk_text)
                    for j, split_text in enumerate(splits):
                        safe_id = unidecode(f"{filename}-p{page_num}-{j}")
                        all_chunks.append({
                            "id": safe_id,
                            "text": split_text,
                            "metadata": {
                                "source": filename,
                                "page": page_num,
                                "type": "text",
                                "text": split_text
                            }
                        })
                else:  # Image description
                    safe_id = unidecode(f"{filename}-p{page_num}-img{chunk.get('image_num', i)}")
                    all_chunks.append({
                        "id": safe_id,
                        "text": chunk_text,
                        "metadata": {
                            "source": filename,
                            "page": page_num,
                            "type": "image_description",
                            "text": chunk_text
                        }
                    })

            if not all_chunks:
                continue

            print(f"Found {len(all_chunks)} chunks ({sum(1 for c in all_chunks if c['metadata']['type'] == 'text')} text, {sum(1 for c in all_chunks if c['metadata']['type'] == 'image_description')} images)")

    # Batch upsert to Pinecone
    for i in range(0, len(all_chunks), batch_size):
        batch = all_chunks[i:i + batch_size]
        vectors_to_upsert = []
        for chunk_data in batch:
            embedding = get_embedding(chunk_data['text'], genai, text_model)
            if embedding:
                vectors_to_upsert.append({
                    "id": chunk_data['id'],
                    "values": embedding,
                    "metadata": chunk_data['metadata']
                })
        if vectors_to_upsert:
            index.upsert(vectors=vectors_to_upsert)
            print(f"Uploading batch {i//batch_size + 1} of {len(all_chunks)//batch_size + 1}")
            print(f"✅ Uploaded {len(vectors_to_upsert)} vectors.")

    print("✅ Script execution completed.")

# ----------------------
# Embedding Generation with Retry Logic
def get_embedding(text, genai, text_model, retries=3, delay=5):
    for attempt in range(retries):
        try:
            response = genai.embed_content(
                model="models/embedding-001",
                content=text,
            )
            return response["embedding"]
        except Exception as e:
            print(f"⚠️ Embedding failed (attempt {attempt + 1}): {e}")
            if attempt < retries - 1:
                print(f"Retrying in {delay} seconds...")
                time.sleep(delay)
            else:
                print("❌ Skipping this chunk after multiple failed attempts.")
                return None

# ----------------------
# Main execution
# ----------------------
if __name__ == "__main__":
    if text_model is not None:
        process_all_pdfs_and_upload_to_pinecone(genai, text_model, pdf_folder=pdf_folder)
    else:
        print("❌ Não é possível processar PDFs sem acesso à API Gemini")
