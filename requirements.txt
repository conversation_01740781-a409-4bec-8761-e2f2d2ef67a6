# Existing content
pypdf
pdf2image
pytesseract
# Para macOS, instale poppler via Homebrew: brew install poppler
python-dotenv==1.0.0
pinecone-client==3.1.0
unidecode==1.3.7
mistralai==0.4.2 # Versão estável com nova sintaxe
pymupdf  # Substituindo pdfplumber para melhor extração de imagens
tiktoken>=0.7.0,<1.0.0
Pillow
opencv-python>=4.8.0
numpy>=1.24.0
pydantic==2.8.2  # Versão compatível com mistralai e fastapi
protobuf==4.24.4
packaging>=24.2,<25.0
pdfminer.six==20221105
google-generativeai>=0.5.0
fastapi
uvicorn[standard]
python-multipart
# Se não usa pdfplumber, pode remover do projeto para evitar conflitos
